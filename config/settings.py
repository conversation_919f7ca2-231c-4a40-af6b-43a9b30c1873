import os
from dotenv import load_dotenv

load_dotenv() # Ładuje zmienne z pliku .env

# Dane logowania
FACEBOOK_EMAIL = os.getenv("FACEBOOK_EMAIL")
FACEBOOK_PASSWORD = os.getenv("FACEBOOK_PASSWORD")

# Ustawienia LLM (OpenRouter)
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
OPENROUTER_MODEL = os.getenv("OPENROUTER_MODEL", "anthropic/claude-3.5-sonnet")

# Ustawienia aplikacji
FACEBOOK_SEARCH_URL = "https://www.facebook.com/marketplace/np/115863338427438?radius=60"
DATABASE_PATH = "monitoring.db"
