import sqlite3
from config.settings import DATABASE_PATH

def get_db_connection():
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def initialize_database():
    """<PERSON><PERSON><PERSON> ta<PERSON>, jeśli nie istnieje."""
    conn = get_db_connection()
    conn.execute("""
        CREATE TABLE IF NOT EXISTS processed_offers (
            id TEXT PRIMARY KEY,
            processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    conn.commit()
    conn.close()

def is_offer_processed(offer_id: str) -> bool:
    """Sprawd<PERSON>, czy oferta była już przetwarzana."""
    conn = get_db_connection()
    cursor = conn.execute("SELECT id FROM processed_offers WHERE id = ?", (offer_id,))
    result = cursor.fetchone()
    conn.close()
    return result is not None

def mark_offer_as_processed(offer_id: str):
    """Zaznacza ofertę jako przet<PERSON><PERSON>oną."""
    conn = get_db_connection()
    conn.execute("INSERT INTO processed_offers (id) VALUES (?)", (offer_id,))
    conn.commit()
    conn.close()
