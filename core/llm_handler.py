import os
import json
# from openai import OpenAI # lub import google.generativeai as genai

# Inicjalizacja klienta
# client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def extract_details_from_text(full_text: str) -> dict:
    """Używa LLM do wyodrębnienia tytułu i ceny z pełnego tekstu strony oferty."""
    prompt = f"""
    Z podanego tekstu oferty, wyodrębnij tytuł przedmiotu, opis i jego cenę.
    Cena powinna być liczbą zmiennoprzecinkową.
    Zwróć wynik w formacie JSON: {{"title": "Tytuł przedmiotu", "price": 123.45}}

    Tekst oferty:
    {full_text[:2000]} # Limit tekstu, aby uniknąć zbyt długich promptów

    JSON:
    """
    # ... logika wysłania promptu do LLM i zwrócenia odpowiedzi ...
    # mock
    try:
        # Symulacja odpowiedzi LLM
        mock_response = {
            "title": "Wyodrębniony tytuł z LLM (mock)",
            "price": 1234.56
        }
        return mock_response
    except json.JSONDecodeError:
        print("LLM zwrócił nieprawidłowy format JSON dla ekstrakcji szczegółów.")
        return {"title": "Nieznany tytuł", "price": 0.0}

def normalize_title_for_search(title: str) -> str:
    """Używa LLM do wyczyszczenia tytułu i stworzenia uniwersalnego zapytania."""
    prompt = f"""
    Przekształć poniższy tytuł oferty w czyste, zwięzłe zapytanie, które będzie można użyć do wyszukania tego samego przedmiotu w innych serwisach. Usuń zbędne słowa jak "sprzedam", "okazja", "stan idealny" i zostaw tylko kluczowe informacje (marka, model, wariant).

    Tytuł oryginalny: "{title}"
    Zapytanie:
    """
    # ... logika wysłania promptu do LLM i zwrócenia odpowiedzi ...
    # mock
    return title # Na razie zwracamy tytuł bez zmian, ponieważ został już wyodrębniony przez LLM

def analyze_prices(main_offer_details: dict, olx: list[dict]) -> str:
    """Używa LLM do porównania cen i wygenerowania podsumowania."""
    # main_offer_details będzie teraz zawierać 'title' i 'price' wyodrębnione przez extract_details_from_text
    prompt = f"""
    Jesteś analitykiem cenowym. Przeanalizuj poniższe dane.
    Oferta z Facebooka: {main_offer_details['title']} za {main_offer_details['price']} PLN.

    Znalezione oferty na OLX:
    {olx}


    Napisz krótkie podsumowanie: Czy oferta z Facebooka jest atrakcyjna cenowo w porównaniu do średnich cen na OLX? Uzasadnij.
    """
    # ... logika wysłania promptu do LLM ...
    # mock
    return "Oferta na Facebooku jest o 10% tańsza niż średnia cena na OLX. Może być dobrą okazją."
