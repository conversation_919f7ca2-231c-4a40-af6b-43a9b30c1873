import re
import traceback
from playwright.sync_api import Page # Dodano import Page
from scrapers.facebook_scraper import FacebookScraper
from scrapers import olx_scraper
from core import llm_handler, data_manager
from config.settings import FACEBOOK_SEARCH_URL

STORAGE_STATE_FILE = "facebook_storage.json"

def run_monitoring_cycle(page: Page): # Przyjmujemy page jako argument
    """Pojedynczy cykl monitorowania i porównywania ofert."""
    # Utworzenie instancji scrapera
    fb_scraper = FacebookScraper(page)
    # Pobierz browser z page dla innych scraperów
    browser = page.context.browser

    try:
        # Krok 1: Pobierz linki do nowych ofert
        print("1. Pobieranie linków do nowych ofert z Facebook Marketplace...")
        offer_urls = fb_scraper.scrape_new_offer_urls(FACEBOOK_SEARCH_URL)

        if not offer_urls:
            print("Nie znaleziono żadnych nowych ofert. Kończę cykl.")
            return

        # Krok 2: Prz<PERSON>war<PERSON><PERSON> każd<PERSON> ofertę po kolei
        for url in offer_urls:
            try:
                # Wyciągnij ID z URL, aby sprawd<PERSON>, czy już było przetwarzane
                match = re.search(r'/item/(\d+)/', url)
                if not match:
                    print(f"Ostrzeżenie: Nie można wyodrębnić ID z URL: {url}")
                    continue
                offer_id = f"fb_{match.group(1)}"

                if data_manager.is_offer_processed(offer_id):
                    print(f"Oferta '{offer_id}' została już przetworzona. Pomijam.")
                    continue

                # Pobierz szczegóły oferty (teraz zawiera tytuł i cenę)
                offer_details = fb_scraper.scrape_offer_details(url)

                if not offer_details:
                    continue # Przejdź do następnej, jeśli wystąpił błąd (błąd już zalogowany w scraperze)

                print(f"Znaleziono nową ofertę: {offer_details['title']} za {offer_details['price']} PLN")

                # Kroki 2-4: Normalizacja, wyszukiwanie w innych serwisach, analiza LLM
                print("2. Normalizacja tytułu i opisu za pomocą LLM...")
                search_data = llm_handler.normalize_title_for_search(
                    offer_details['title'],
                    offer_details.get('description', '')
                )
                print(f"   > Zapytania: {search_data['queries']}")
                print(f"   > Porównywalny: {search_data['isComparable']}")

                if not search_data['isComparable']:
                    print("   > Produkt oceniony jako nieporównywalny - pomijam wyszukiwanie na OLX")
                    analysis = "Produkt został oceniony jako nieporównywalny na polskim rynku (np. unikalna odzież zagranicznej marki). Porównanie cen zostało pominięte."
                    unique_olx_results = []
                else:
                    print("3. Wyszukiwanie na OLX...")
                    all_olx_results = []

                    # Wyszukaj używając wszystkich zapytań
                    for i, query in enumerate(search_data['queries'], 1):
                        print(f"   > Zapytanie {i}: {query}")
                        olx_results = olx_scraper.search(browser, query)
                        all_olx_results.extend(olx_results)
                        print(f"     Znaleziono {len(olx_results)} ofert")

                    # Usuń duplikaty na podstawie URL
                    unique_olx_results = []
                    seen_urls = set()
                    for offer in all_olx_results:
                        if offer['url'] not in seen_urls:
                            unique_olx_results.append(offer)
                            seen_urls.add(offer['url'])

                    print(f"   > Łącznie {len(unique_olx_results)} unikalnych ofert z OLX")

                    print("4. Analiza cenowa za pomocą LLM...")
                    analysis = llm_handler.analyze_prices(offer_details, unique_olx_results)

                print("\n--- WYNIK ANALIZY ---")
                print(analysis)
                print("---------------------\n")

                # Zapisz wyniki analizy do bazy danych
                print("5. Zapisywanie wyników do bazy danych...")
                data_manager.save_analysis_result(offer_details, search_data, unique_olx_results, analysis)

                # Zapisz, że oferta została przetworzona
                data_manager.mark_offer_as_processed(offer_details['id'])
            except Exception as e:
                print(f"Błąd podczas przetwarzania oferty {url}: {e}")
                print(traceback.format_exc())

    except Exception as e:
        print(f"Wystąpił ogólny błąd w cyklu monitorowania: {e}")
        print(traceback.format_exc())
    finally:
        print("Zakończono przetwarzanie wszystkich znalezionych ofert.")

