import os
import re
import traceback
from playwright.sync_api import Page # Dodano import Page
from scrapers.facebook_scraper import FacebookScraper
from scrapers import olx_scraper, allegro_scraper
from core import llm_handler, data_manager
from config.settings import FACEBOOK_SEARCH_URL

STORAGE_STATE_FILE = "facebook_storage.json"

def run_monitoring_cycle(page: Page): # Przyjmujemy page jako argument
    """Pojedynczy cykl monitorowania i porównywania ofert."""
    # Utworzenie instancji scrapera
    fb_scraper = FacebookScraper(page)

    try:
        # Krok 1: Pobierz linki do nowych ofert
        print("1. Pobieranie linków do nowych ofert z Facebook Marketplace...")
        offer_urls = fb_scraper.scrape_new_offer_urls(FACEBOOK_SEARCH_URL)

        if not offer_urls:
            print("Nie znaleziono żadnych nowych ofert. Kończę cykl.")
            return

        # Krok 2: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ofertę po kolei
        for url in offer_urls:
            try:
                # Wyciągnij ID z URL, aby sprawd<PERSON>, czy już było przetwarzane
                match = re.search(r'/item/(\d+)/', url)
                if not match:
                    print(f"Ostrzeżenie: Nie można wyodrębnić ID z URL: {url}")
                    continue
                offer_id = f"fb_{match.group(1)}"

                if data_manager.is_offer_processed(offer_id):
                    print(f"Oferta '{offer_id}' została już przetworzona. Pomijam.")
                    continue

                # Pobierz szczegóły oferty (teraz zawiera full_text)
                scraped_data = fb_scraper.scrape_offer_details(url)

                if not scraped_data:
                    continue # Przejdź do następnej, jeśli wystąpił błąd (błąd już zalogowany w scraperze)

                # Krok 3: Wyodrębnij tytuł i cenę za pomocą LLM
                print("2. Wyodrębnianie tytułu i ceny za pomocą LLM...")
                extracted_details = llm_handler.extract_details_from_text(scraped_data['full_text'])
                
                # Połącz dane
                offer_details = {
                    "id": scraped_data['id'],
                    "url": scraped_data['url'],
                    "title": extracted_details.get('title', 'Brak tytułu'),
                    "price": extracted_details.get('price', 0.0)
                }

                print(f"Znaleziono nową ofertę: {offer_details['title']} za {offer_details['price']} PLN")

                # Kroki 4-6: Normalizacja, wyszukiwanie w innych serwisach, analiza LLM
                print("3. Normalizacja tytułu za pomocą LLM...")
                normalized_query = llm_handler.normalize_title_for_search(offer_details['title'])
                print(f"   > Znormalizowane zapytanie: {normalized_query}")

                print("4. Wyszukiwanie na OLX i Allegro...")
                olx_results = olx_scraper.search(normalized_query)
                allegro_results = allegro_scraper.search(normalized_query)

                print("5. Analiza cenowa za pomocą LLM...")
                analysis = llm_handler.analyze_prices(offer_details, olx_results, allegro_results)
                
                print("\n--- WYNIK ANALIZY ---")
                print(analysis)
                print("---------------------\n")

                # Zapisz, że oferta została przetworzona
                data_manager.mark_offer_as_processed(offer_details['id'])
            except Exception as e:
                print(f"Błąd podczas przetwarzania oferty {url}: {e}")
                print(traceback.format_exc())

    except Exception as e:
        print(f"Wystąpił ogólny błąd w cyklu monitorowania: {e}")
        print(traceback.format_exc())
    finally:
        print("Zakończono przetwarzanie wszystkich znalezionych ofert.")

