import re
import time
import traceback
from playwright.sync_api import <PERSON><PERSON><PERSON>, TimeoutError as PlaywrightTimeoutError

def search(browser: Browser, query: str) -> list[dict]:
    """Wyszu<PERSON><PERSON> oferty na Allegro i zwraca listę słowników z detalami."""
    offers = []
    page = browser.new_page()
        
    try:
        search_url = f"https://allegro.pl/listing?string={query}"
        print(f"Wyszukuję na Allegro: {search_url}")
        page.goto(search_url, wait_until="domcontentloaded")
        time.sleep(3) # Początkowe oczekiwanie na załadowanie

        # Akceptacja ciasteczek, jeśli przycisk jest widoczny
        try:
            # Przycisk do akceptacji ciasteczek na Allegro
            page.locator("button[data-role='accept-button']").click(timeout=5000)
            print("Zaakceptowano ciasteczka na Allegro.")
        except PlaywrightTimeoutError:
            print("Banner ciasteczek na Allegro nie został znaleziony, kontynuuję.")

        # Scrollowanie, aby zała<PERSON><PERSON>ć więcej ofert
        for _ in range(2): # Scrolluj 2 razy, aby załadować więcej wyników
            page.mouse.wheel(0, 10000) # Scrolluj w dół o 10000 pikseli
            time.sleep(2)

        # Selektory dla ofert na Allegro (mogą wymagać dostosowania)
        # Szukamy kontenerów ofert, a następnie elementów w ich obrębie
        # Allegro ma bardzo dynamiczne selektory, to może być trudne.
        # Spróbujmy po ogólnym kontenerze oferty, a potem po elementach tekstowych.
        offer_elements = page.locator("article[data-analytics-view-custom-page='listing']").all()
        print(f"Znaleziono {len(offer_elements)} potencjalnych ofert na Allegro.")

        for offer_element in offer_elements:
            try:
                # Tytuł
                title_locator = offer_element.locator("h2 > a")
                title = title_locator.inner_text()
                url = title_locator.get_attribute("href")

                # Cena
                price_text = offer_element.locator("div[data-test-id='price-main']").inner_text()
                price = float(re.sub(r'[^\\d,.]', '', price_text).replace(',', '.'))

                if title and price and url:
                    offers.append({"title": title, "price": price, "url": url})
            except Exception as e:
                # Błąd parsowania pojedynczej oferty, kontynuujemy z następną
                print(f"Błąd podczas parsowania pojedynczej oferty Allegro: {e}")
                print(traceback.format_exc())
                continue

    except Exception as e:
        print(f"Wystąpił błąd podczas wyszukiwania na Allegro: {e}")
        print(traceback.format_exc())
    finally:
        page.close()
    return offers
