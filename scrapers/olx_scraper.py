import re
import time
import traceback
from playwright.sync_api import <PERSON><PERSON>er, TimeoutError as PlaywrightTimeoutError

def search(browser: Browser, query: str) -> list[dict]:
    """Wys<PERSON><PERSON>je oferty na OLX i zwraca listę słowników z detalami."""
    offers = []
    page = browser.new_page()
        
    try:
        search_url = f"https://www.olx.pl/oferty/q-{query}/"
        print(f"Wyszukuję na OLX: {search_url}")
        page.goto(search_url, wait_until="domcontentloaded")
        time.sleep(3) # Początkowe oczekiwanie na załadowanie

        # Scrollowanie, aby zała<PERSON><PERSON><PERSON> więcej ofert
        for _ in range(2): # Scrolluj 2 razy, aby załadować więcej wyników
            page.mouse.wheel(0, 10000) # Scrolluj w dół o 10000 pikseli
            time.sleep(2)

        # Selektory dla ofert na OLX (mogą wymagać dostosowania)
        # <PERSON><PERSON><PERSON> konte<PERSON>ów ofert, a następnie elementów w ich obrębie
        offer_elements = page.locator("div[data-cy='l-card']").all()
        print(f"Znaleziono {len(offer_elements)} potencjalnych ofert na OLX.")

        for offer_element in offer_elements:
            try:
                title = offer_element.locator("h6[data-cy='l-card-title']").inner_text()
                price_text = offer_element.locator("p[data-cy='l-card-price']").inner_text()
                price = float(re.sub(r'[^\\d,.]', '', price_text).replace(',', '.'))
                url = offer_element.locator("a[data-cy='l-card-link']").get_attribute("href")

                if title and price and url:
                    offers.append({"title": title, "price": price, "url": url})
            except Exception as e:
                # Błąd parsowania pojedynczej oferty, kontynuujemy z następną
                print(f"Błąd podczas parsowania pojedynczej oferty OLX: {e}")
                print(traceback.format_exc())
                continue

    except Exception as e:
        print(f"Wystąpił błąd podczas wyszukiwania na OLX: {e}")
        print(traceback.format_exc())
    finally:
        page.close()
    return offers
