#!/usr/bin/env python3
"""
Narzędzie do przeglądania historii analiz z bazy danych.
"""

import json
from core.data_manager import get_analysis_history

def format_analysis_result(result):
    """Formatuje pojedynczy wynik analizy do czytelnej formy."""
    print(f"\n{'='*80}")
    print(f"ID: {result['id']} | Data: {result['created_at']}")
    print(f"Oferta Facebook: {result['offer_id']}")
    print(f"{'='*80}")
    
    print(f"📱 TYTUŁ: {result['facebook_title']}")
    print(f"💰 CENA: {result['facebook_price']} PLN")
    print(f"🔗 URL: {result['facebook_url']}")
    
    if result['facebook_description'] and result['facebook_description'] != "Brak opisu":
        desc = result['facebook_description'][:200] + "..." if len(result['facebook_description']) > 200 else result['facebook_description']
        print(f"📝 OPIS: {desc}")
    
    # Zapytania wyszukiwania
    try:
        queries = json.loads(result['search_queries'])
        print(f"🔍 ZAPYTANIA: {', '.join(queries)}")
    except:
        print(f"🔍 ZAPYTANIA: {result['search_queries']}")
    
    print(f"⚖️ PORÓWNYWALNY: {'TAK' if result['is_comparable'] else 'NIE'}")
    print(f"📊 OFERTY OLX: {result['olx_offers_count']}")
    
    print(f"\n📋 ANALIZA:")
    print(f"{result['analysis_text']}")
    print(f"{'='*80}")

def show_recent_analyses(limit=10):
    """Wyświetla ostatnie analizy."""
    print(f"📈 OSTATNIE {limit} ANALIZ")
    print(f"{'='*80}")
    
    results = get_analysis_history(limit)
    
    if not results:
        print("Brak zapisanych analiz w bazie danych.")
        return
    
    for result in results:
        format_analysis_result(result)

def show_profitable_analyses(limit=20):
    """Wyświetla analizy z potencjałem handlowym."""
    print(f"💰 ANALIZY Z POTENCJAŁEM HANDLOWYM")
    print(f"{'='*80}")
    
    results = get_analysis_history(limit)
    
    profitable_keywords = [
        "potencjał handlowy", "opłaca się", "marża", "zysk", 
        "atrakcyjna cena", "dobra okazja", "warto kupić"
    ]
    
    profitable_results = []
    for result in results:
        analysis_lower = result['analysis_text'].lower()
        if any(keyword in analysis_lower for keyword in profitable_keywords):
            profitable_results.append(result)
    
    if not profitable_results:
        print("Brak analiz z wyraźnym potencjałem handlowym.")
        return
    
    print(f"Znaleziono {len(profitable_results)} potencjalnie zyskownych ofert:")
    
    for result in profitable_results:
        format_analysis_result(result)

def main():
    """Główna funkcja narzędzia."""
    print("🔍 PRZEGLĄDARKA HISTORII ANALIZ")
    print("1. Ostatnie analizy")
    print("2. Analizy z potencjałem handlowym")
    print("3. Wszystkie analizy (ostatnie 50)")
    
    choice = input("\nWybierz opcję (1-3): ").strip()
    
    if choice == "1":
        limit = input("Ile analiz wyświetlić? (domyślnie 10): ").strip()
        limit = int(limit) if limit.isdigit() else 10
        show_recent_analyses(limit)
    elif choice == "2":
        show_profitable_analyses()
    elif choice == "3":
        show_recent_analyses(50)
    else:
        print("Nieprawidłowy wybór.")

if __name__ == "__main__":
    main()
